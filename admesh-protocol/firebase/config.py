import os
import json

# Try to load .env file if it exists (for local development)
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv might not be installed in production
    pass

import firebase_admin
from firebase_admin import credentials, firestore

firebase_app = None
db = None

def initialize_firebase():
    global firebase_app, db

    if firebase_admin._apps:
        return firestore.client()

    try:
        credentials_value = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        # Default to development if running locally and ENV not explicitly set
        is_cloud_run = os.getenv("K_SERVICE") is not None  # Cloud Run sets this
        env = os.getenv("ENV", "production" if is_cloud_run else "development")
        print("📦 ENV =", env)

        if not credentials_value:
            # If no credentials path is set, use the appropriate default file
            if env == "development":
                credentials_value = os.path.join(os.path.dirname(__file__), "dev-serviceAccountKey.json")
            else:
                credentials_value = os.path.join(os.path.dirname(__file__), "serviceAccountKey.json")
            print(f"Using default credentials file: {credentials_value}")

        # ✅ In production (Cloud Run): treat as raw JSON string
        if env == "production":
            cred_dict = json.loads(credentials_value)
            cred = credentials.Certificate(cred_dict)
        else:
            # ✅ In local dev: treat as file path
            if os.path.exists(credentials_value):
                cred = credentials.Certificate(credentials_value)
            else:
                raise ValueError(f"Credentials file does not exist: {credentials_value}")

        firebase_app = firebase_admin.initialize_app(cred)
        db = firestore.client()
        return db

    except Exception as e:
        print(f"🔥 Error initializing Firebase: {e}")
        raise e

def get_db():
    global db
    if db is None:
        return initialize_firebase()
    return db
