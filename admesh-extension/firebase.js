// Firebase configuration for AdMesh extension
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { getAuth, onAuthStateChanged, signOut, signInWithEmailAndPassword, createUserWithEmailAndPassword, GoogleAuthProvider, signInWithPopup, signInWithRedirect, getRedirectResult } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Firebase configuration - same as dashboard
const firebaseConfig = {
  apiKey: "AIzaSyDxBNmjuoMjkS5u8iad6PSB_5Lm7ggIkfY",
  authDomain: "admesh-dev.firebaseapp.com",
  projectId: "admesh-dev",
  storageBucket: "admesh-dev.firebasestorage.app",
  messagingSenderId: "651813374456",
  appId: "1:651813374456:web:dfc618425534b042576e0d"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

// Helper function to get current user's ID token
async function getIdToken() {
  const user = auth.currentUser;
  if (!user) return null;
  return await user.getIdToken();
}

// Sign in with email and password
async function signInWithEmail(email, password) {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error('Error signing in with email and password:', error);
    throw error;
  }
}

// Sign in with Google
async function signInWithGoogle(role = 'user') {
  try {
    // Sign in with Google popup
    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;

    // Get ID token for API call
    const idToken = await user.getIdToken();

    // Register with backend to set role
    const response = await fetch('http://localhost:8000/auth/google-onboard', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${idToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ role })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to register with Google');
    }

    return user;
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
}

// Sign up with email and password
async function signUpWithEmail(email, password, role = 'user') {
  try {
    // Create user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Get ID token for API call
    const idToken = await user.getIdToken();

    // Register with backend to set role
    const response = await fetch('http://localhost:8000/auth/register', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${idToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ role })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to register user role');
    }

    return user;
  } catch (error) {
    console.error('Error signing up with email and password:', error);
    throw error;
  }
}

// Redirect to useadmesh.com
function redirectToUseadmesh(path = '') {
  window.open(`https://useadmesh.com${path}`, '_blank');
}

// Sign out
async function signOutUser() {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
}

// Get user role from token
async function getUserRole() {
  const user = auth.currentUser;
  if (!user) return null;

  try {
    const idTokenResult = await user.getIdTokenResult();
    return idTokenResult.claims.role;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
}

// Export the functions and objects
export {
  auth,
  googleProvider,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  getIdToken,
  signInWithEmail,
  signInWithGoogle,
  signUpWithEmail,
  redirectToUseadmesh,
  signOutUser,
  getUserRole,
  onAuthStateChanged
};
